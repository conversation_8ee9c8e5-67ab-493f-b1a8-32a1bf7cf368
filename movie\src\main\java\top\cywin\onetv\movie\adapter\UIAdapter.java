package top.cywin.onetv.movie.adapter;

import android.util.Log;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.event.*;

/**
 * UI适配器 - 纯粹的EventBus事件适配器
 * 只负责FongMi_TV事件与Compose UI的事件转换
 */
public class UIAdapter {

    private static final String TAG = "UIAdapter";

    public UIAdapter() {
        Log.d(TAG, "🏗️ UIAdapter 初始化完成");
    }

    /**
     * 初始化EventBus监听 - 只做事件转换
     */
    public void initializeEventBus() {
        Log.d(TAG, "🔄 初始化EventBus监听");
        try {
            // ✅ 注册EventBus监听器
            EventBus.getDefault().register(this);
            Log.d(TAG, "✅ EventBus监听初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ EventBus监听初始化失败", e);
            throw new RuntimeException("EventBus监听初始化失败", e);
        }
    }

    /**
     * 清理EventBus监听
     */
    public void cleanup() {
        Log.d(TAG, "🧹 清理EventBus监听");
        try {
            EventBus.getDefault().unregister(this);
            Log.d(TAG, "✅ EventBus监听清理完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ EventBus监听清理失败", e);
        }
    }

    // ✅ 监听FongMi_TV的SiteViewModel事件，转换为Compose UI事件
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSiteViewModelResult(Object result) {
        Log.d(TAG, "📡 收到SiteViewModel结果事件");

        // ✅ 转换为Compose UI事件
        if (result instanceof top.cywin.onetv.movie.bean.Result) {
            top.cywin.onetv.movie.bean.Result vodResult = (top.cywin.onetv.movie.bean.Result) result;

            // 判断结果类型并发送相应的Compose事件
            if (vodResult.getList() != null && !vodResult.getList().isEmpty()) {
                // 搜索结果或分类结果
                EventBus.getDefault().post(new SearchResultEvent(
                        vodResult.getList(),
                        "",
                        vodResult.getList().size() >= 20,
                        1,
                        vodResult.getTotal()));
            }
        }
    }

    // ✅ 防止循环的标志
    private boolean isHandlingConfigUpdate = false;

    // ✅ 监听FongMi_TV的配置更新事件 - 完全修复事件循环问题
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onConfigUpdate(Object configEvent) {
        String eventClassName = configEvent.getClass().getSimpleName();
        Log.d("VOD_FLOW", "[UI_ADAPTER] 收到事件: " + eventClassName);
        Log.d(TAG, "⚙️ 收到配置更新事件: " + eventClassName);

        // ✅ 防止循环处理
        if (isHandlingConfigUpdate) {
            Log.w(TAG, "⚠️ UIAdapter正在处理配置更新，跳过重复事件");
            return;
        }

        // ✅ 处理ConfigUpdateEvent - 这是我们需要的配置更新事件
        if (configEvent instanceof ConfigUpdateEvent) {
            ConfigUpdateEvent updateEvent = (ConfigUpdateEvent) configEvent;
            String message = updateEvent.getErrorMessage() != null ? updateEvent.getErrorMessage() : "配置更新";
            Log.d(TAG, "⚙️ 收到配置更新事件: " + message);
            Log.d(TAG, "📊 配置状态: " + (updateEvent.isSuccess() ? "成功" : "失败"));

            // 直接处理ConfigUpdateEvent，不需要再次转换
            return;
        }

        // ✅ 只处理真正的FongMi_TV配置相关事件
        String packageName = configEvent.getClass().getPackage() != null ? configEvent.getClass().getPackage().getName()
                : "";

        // 🔥 修复：只跳过已经处理的事件，允许UI事件通过
        if (!packageName.startsWith("top.cywin.onetv.movie") ||
                eventClassName.equals("ConfigUpdateEvent") ||
                eventClassName.equals("SearchResultEvent") ||
                eventClassName.equals("PlayUrlParseEvent") ||
                eventClassName.equals("ErrorEvent")) {
            Log.d("VOD_FLOW", "[UI_ADAPTER] ⚠️ 跳过已处理事件: " + eventClassName);
            return;
        }

        // 🔥 修复：处理UI事件，不再跳过
        if (eventClassName.equals("ShowSiteSelectorEvent") ||
                eventClassName.equals("ShowRouteSelectorEvent") ||
                eventClassName.equals("ShowHistoryScreenEvent") ||
                eventClassName.equals("ShowFavoritesScreenEvent") ||
                eventClassName.equals("HomeContentEvent") ||
                eventClassName.equals("CategoryContentEvent")) {
            Log.d("VOD_FLOW", "[UI_ADAPTER] ✅ 处理UI事件: " + eventClassName);
            // 直接转发UI事件，不做额外处理
            return;
        }

        // 🔥 修复：特殊处理RouteChangeEvent，需要转发给MovieHomeScreen
        if (eventClassName.equals("RouteChangeEvent")) {
            Log.d("VOD_FLOW", "[UI_ADAPTER] ✅ 转发RouteChangeEvent到MovieHomeScreen");
            // RouteChangeEvent需要被转发，不能直接return
            // 让它继续执行，不做任何处理，EventBus会自动转发
        }

        // 只处理真正的配置更新事件
        if (!eventClassName.contains("RefreshEvent") &&
                !eventClassName.contains("Config") &&
                !eventClassName.contains("Site") &&
                !eventClassName.contains("Parse")) {
            Log.w(TAG, "⚠️ 跳过非配置相关事件: " + eventClassName);
            return;
        }

        isHandlingConfigUpdate = true;
        try {
            Log.d("VOD_FLOW", "[UI_ADAPTER] 🔥 处理FongMi_TV配置更新事件: " + eventClassName);
            Log.d("VOD_FLOW", "[UI_ADAPTER] 🔥 发送ConfigUpdateEvent事件");
            Log.d(TAG, "✅ 处理FongMi_TV配置更新事件: " + eventClassName);
            // ✅ 转换为Compose UI事件 - 在后台线程处理，避免阻塞主线程
            EventBus.getDefault().post(new ConfigUpdateEvent(
                    VodConfig.get(),
                    true,
                    "FongMi_TV配置更新: " + eventClassName));
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置更新事件处理异常", e);
        } finally {
            isHandlingConfigUpdate = false;
        }
    }

    // ✅ 监听FongMi_TV的播放解析事件 - 修复事件循环问题
    // 注意：这个方法被注释掉，因为它会接收所有EventBus事件，包括ConfigUpdateEvent
    // 如果需要处理播放解析事件，应该监听具体的Result类型事件
    /*
     * @Subscribe(threadMode = ThreadMode.BACKGROUND)
     * public void onPlayUrlParsed(top.cywin.onetv.movie.bean.Result parseResult) {
     * try {
     * Log.d(TAG, "🎬 收到播放解析结果");
     * 
     * Log.d(TAG, "✅ 处理播放地址解析结果");
     * EventBus.getDefault().post(new PlayUrlParseEvent(
     * parseResult.getUrl().v(), // 使用v()方法获取URL字符串
     * parseResult.getHeaders(), // 使用getHeaders()方法获取Map<String,String>
     * "",
     * 0,
     * ""
     * ));
     * }
     * } catch (Exception e) {
     * Log.e(TAG, "❌ 播放解析事件处理异常", e);
     * }
     * }
     */

    // ✅ 防止错误事件循环的标志
    private boolean isHandlingError = false;

    // ✅ 监听特定的FongMi_TV错误事件（如果需要的话）
    // 注意：原来的onError方法会接收所有EventBus事件，导致ConfigUpdateEvent被错误处理
    // 如果需要处理特定错误事件，应该创建具体的事件类型方法
    /*
     * @Subscribe(threadMode = ThreadMode.MAIN)
     * public void onSpecificError(SpecificErrorEvent errorEvent) {
     * Log.d(TAG, "❌ 收到特定错误事件: " + errorEvent.getClass().getSimpleName());
     * // 处理特定错误事件的逻辑
     * }
     */

}
