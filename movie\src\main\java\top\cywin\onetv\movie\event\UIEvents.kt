package top.cywin.onetv.movie.event

/**
 * UI事件系统 - 用于FongMi_TV解析引擎与Compose UI的通信
 * 完整的事件定义，支持所有UI交互场景
 */

// ===== 导航事件 =====
sealed class NavigationEvent {
    data class NavigateToDetail(
        val vodId: String,
        val siteKey: String,
        val movieName: String
    ) : NavigationEvent()

    data class NavigateToCategory(
        val typeId: String,
        val typeName: String
    ) : NavigationEvent()

    data class NavigateToSearch(
        val keyword: String
    ) : NavigationEvent()

    data class NavigateToSearchResultList(
        val keyword: String,
        val results: List<top.cywin.onetv.movie.bean.Vod>
    ) : NavigationEvent()

    object NavigateBack : NavigationEvent()
}

// ===== 错误事件 =====
data class UIErrorEvent(
    val message: String,
    val throwable: Throwable? = null,
    val errorCode: String? = null
)



// ===== 配置相关事件 =====
data class ConfigUpdateEvent(
    val config: top.cywin.onetv.movie.api.config.VodConfig?,
    val isSuccess: Boolean,
    val errorMessage: String? = null
)

data class ConfigLoadEvent(
    val isLoading: Boolean,
    val progress: Float = 0f,
    val message: String = ""
)

// ===== 立即导航事件 =====
data class NavigateToSearchResultsEvent(
    val keyword: String
)

// ===== 搜索相关事件 =====
data class SearchResultEvent(
    val results: List<top.cywin.onetv.movie.bean.Vod>,
    val keyword: String,
    val hasMore: Boolean,
    val page: Int = 1,
    val total: Int = 0
)

data class SearchStartEvent(
    val keyword: String,
    val siteKey: String? = null
)

data class SearchErrorEvent(
    val keyword: String,
    val error: String
)

// ===== 内容相关事件 =====
data class ContentDetailEvent(
    val vod: top.cywin.onetv.movie.bean.Vod?,
    val success: Boolean,
    val errorMessage: String? = null
)

data class CategoryContentEvent(
    val vods: List<top.cywin.onetv.movie.bean.Vod>,
    val typeId: String,
    val page: Int,
    val hasMore: Boolean,
    val total: Int = 0
)

data class HomeContentEvent(
    val categories: List<top.cywin.onetv.movie.bean.Class>,
    val recommendVods: List<top.cywin.onetv.movie.bean.Vod>,
    val isSuccess: Boolean
)

// ===== 播放相关事件 =====
data class PlayUrlParseEvent(
    val playUrl: String?,
    val headers: Map<String, String>?,
    val vodId: String? = null,
    val episodeIndex: Int = 0,
    val flag: String? = null,
    val flowId: String? = null // 🔥 新增：FlowID用于全流程跟踪
)

data class PlayUrlParseStartEvent(
    val vodId: String,
    val episodeUrl: String,
    val flag: String
)

data class PlayUrlParseErrorEvent(
    val vodId: String,
    val error: String
)

// ===== WebView解析事件 =====
data class WebViewParseEvent(
    val request: WebViewParseRequest
)

data class WebViewParseRequest(
    val key: String,
    val from: String,
    val headers: Map<String, String>,
    val url: String,
    val click: String,
    val callback: top.cywin.onetv.movie.impl.ParseCallback?,
    val isPlayerUrl: Boolean = false
)

data class WebViewParseSuccessEvent(
    val playUrl: String,
    val headers: Map<String, String>? = null
)

data class WebViewParseErrorEvent(
    val error: String
)

// ===== 收藏和历史事件 =====
data class FavoriteUpdateEvent(
    val vodId: String,
    val isFavorite: Boolean,
    val isSuccess: Boolean
)

data class FavoriteListEvent(
    val favorites: List<top.cywin.onetv.movie.bean.Keep>,
    val success: Boolean,
    val error: String? = null
)

data class HistoryUpdateEvent(
    val vodId: String,
    val position: Long,
    val duration: Long,
    val isSuccess: Boolean
)

data class HistoryListEvent(
    val histories: List<top.cywin.onetv.movie.bean.History>,
    val success: Boolean,
    val error: String? = null
)

data class HistoryLoadEvent(
    val histories: List<Any>, // History对象列表
    val isSuccess: Boolean
)

// ===== 站点相关事件 =====
data class SiteChangeEvent(
    val site: top.cywin.onetv.movie.bean.Site?,
    val isSuccess: Boolean
)

data class SiteListEvent(
    val sites: List<top.cywin.onetv.movie.bean.Site>,
    val success: Boolean,
    val error: String? = null
)

data class SiteSwitchEvent(
    val site: top.cywin.onetv.movie.bean.Site?,
    val success: Boolean,
    val error: String? = null
)

data class SiteListUpdateEvent(
    val sites: List<top.cywin.onetv.movie.bean.Site>,
    val currentSite: top.cywin.onetv.movie.bean.Site?
)

// ===== 云盘相关事件 =====
data class CloudDriveEvent(
    val driveId: String,
    val files: List<Any>, // CloudFile对象列表
    val path: String,
    val isSuccess: Boolean
)

data class CloudDriveConfigEvent(
    val configs: List<Any>, // CloudDriveConfig对象列表
    val isSuccess: Boolean
)

// ===== 直播相关事件 =====
data class LiveChannelEvent(
    val channels: List<Any>, // Live对象列表
    val group: String,
    val isSuccess: Boolean
)

data class LivePlayEvent(
    val playUrl: String,
    val channelName: String,
    val isSuccess: Boolean
)

// ===== 设置相关事件 =====
data class SettingsUpdateEvent(
    val key: String,
    val value: Any,
    val isSuccess: Boolean
)

// ===== 网络相关事件 =====
data class NetworkStatusEvent(
    val isConnected: Boolean,
    val networkType: String
)

data class ApiTestEvent(
    val url: String,
    val isSuccess: Boolean,
    val responseTime: Long = 0,
    val errorMessage: String? = null
)

// ===== 解析相关事件 =====
data class ParseProgressEvent(
    val progress: Float,
    val message: String,
    val currentStep: String
)

data class ParseCompleteEvent(
    val isSuccess: Boolean,
    val result: Any? = null,
    val errorMessage: String? = null
)

// ===== UI状态事件 =====
data class LoadingStateEvent(
    val isLoading: Boolean,
    val message: String = "",
    val progress: Float = 0f
)

data class UIRefreshEvent(
    val type: String, // "home", "category", "search", "detail", "player"
    val params: Map<String, String> = emptyMap()
)

// ===== 系统事件 =====
data class SystemEvent(
    val type: String, // "memory_low", "storage_low", "network_changed"
    val data: Map<String, Any> = emptyMap()
)

// ===== 🔥 新增：首页顶端功能按钮事件 =====
/**
 * 返回直播界面事件
 */
data class NavigateToLiveEvent(
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 显示观看历史界面事件 - 按照架构：导航到完整屏幕
 */
data class ShowHistoryScreenEvent(
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 显示收藏界面事件 - 按照架构：导航到完整屏幕
 */
data class ShowFavoritesScreenEvent(
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 显示站点选择器事件
 */
data class ShowSiteSelectorEvent(
    val sites: List<top.cywin.onetv.movie.bean.Site>,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 显示搜索界面事件
 */
data class ShowSearchScreenEvent(
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 🔥 显示线路选择器事件 - 包含线路列表
 */
data class ShowRouteSelectorEvent(
    val routes: List<top.cywin.onetv.movie.ui.model.VodConfigUrl>,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 🔥 线路切换事件 - 用户主动选择线路时触发配置重新加载
 */
data class RouteChangeEvent(
    val routeUrl: String,
    val timestamp: Long = System.currentTimeMillis()
)


