package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieViewModel
import top.cywin.onetv.movie.viewmodel.MovieUiState
import top.cywin.onetv.movie.bean.Keep
import top.cywin.onetv.movie.MovieApp
import android.util.Log

/**
 * OneTV Movie收藏页面 - 按照FongMi_TV整合指南重构
 * 遵循项目架构：通过适配器调用FongMi_TV的完整功能
 */
@Composable
fun MovieFavoritesScreen(
    navController: NavController,
    viewModel: MovieViewModel = viewModel {
        MovieViewModel()
    }
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        // ✅ 通过适配器系统加载收藏记录
        viewModel.loadFavorites()
    }

    // ✅ UI内容渲染
    FavoritesContent(
        uiState = uiState,
        onBack = { navController.popBackStack() },
        onFavoriteClick = { favorite ->
            // 🔥 按照架构：通过适配器处理收藏点击，导航到详情页面
            val movieApp = MovieApp.getInstance()
            movieApp.repositoryAdapter.handleMovieClick(
                favorite.key.split(top.cywin.onetv.movie.database.AppDatabase.SYMBOL)[1], // vodId
                favorite.vodName ?: "",
                favorite.key.split(top.cywin.onetv.movie.database.AppDatabase.SYMBOL)[0] // siteKey
            )
        },
        onFavoriteDelete = { favorite -> 
            viewModel.deleteFavorite(favorite.key)
        },
        onClearAllFavorites = { viewModel.clearAllFavorites() },
        onRefresh = { viewModel.loadFavorites() },
        onError = { viewModel.clearError() }
    )
}

@Composable
private fun FavoritesContent(
    uiState: MovieUiState,
    onBack: () -> Unit,
    onFavoriteClick: (Keep) -> Unit,
    onFavoriteDelete: (Keep) -> Unit,
    onClearAllFavorites: () -> Unit,
    onRefresh: () -> Unit,
    onError: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        @OptIn(ExperimentalMaterial3Api::class)
        TopAppBar(
            title = {
                Text(
                    text = "我的收藏",
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                // 清空收藏按钮
                if (uiState.favoritesList.isNotEmpty()) {
                    IconButton(onClick = onClearAllFavorites) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "清空收藏"
                        )
                    }
                }
            }
        )

        // 内容区域
        when {
            uiState.isLoadingFavorites -> {
                LoadingScreen(message = "正在加载收藏...")
            }
            uiState.error != null -> {
                val errorMessage = uiState.error ?: "未知错误"
                ErrorScreen(
                    error = errorMessage,
                    onRetry = onRefresh,
                    onBack = onBack
                )
            }
            uiState.favoritesList.isEmpty() -> {
                EmptyFavoritesScreen()
            }
            else -> {
                FavoritesListScreen(
                    favorites = uiState.favoritesList,
                    onFavoriteClick = onFavoriteClick,
                    onFavoriteDelete = onFavoriteDelete
                )
            }
        }
    }
}

@Composable
private fun EmptyFavoritesScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Favorite,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暂无收藏内容",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun FavoritesListScreen(
    favorites: List<Keep>,
    onFavoriteClick: (Keep) -> Unit,
    onFavoriteDelete: (Keep) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(favorites) { favorite ->
            FavoriteItem(
                favorite = favorite,
                onClick = { onFavoriteClick(favorite) },
                onDelete = { onFavoriteDelete(favorite) }
            )
        }
    }
}

@Composable
private fun FavoriteItem(
    favorite: Keep,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 电影海报占位符
            Box(
                modifier = Modifier
                    .width(60.dp)
                    .height(80.dp)
                    .background(MaterialTheme.colorScheme.surfaceVariant),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Movie,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 电影信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = favorite.vodName ?: "未知影片",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = "站点: ${favorite.siteName ?: "未知站点"}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(favorite.createTime)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 操作按钮
            Row {
                IconButton(onClick = onClick) {
                    Icon(Icons.Default.PlayArrow, contentDescription = "播放")
                }
                IconButton(onClick = onDelete) {
                    Icon(Icons.Default.Delete, contentDescription = "删除")
                }
            }
        }
    }
}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}
