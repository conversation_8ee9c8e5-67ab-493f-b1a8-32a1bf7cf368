package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import kotlinx.coroutines.delay
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountTree
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Tv
import androidx.compose.material.icons.filled.Tune
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material.icons.filled.Movie
import androidx.compose.material.icons.filled.CloudDownload
import androidx.compose.material.icons.filled.HighQuality
import androidx.compose.material.icons.filled.Speed
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Router
import androidx.compose.material.icons.filled.Error
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import android.util.Log
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.viewmodel.MovieViewModel
import top.cywin.onetv.movie.viewmodel.MovieUiState
import top.cywin.onetv.movie.event.NavigationEvent
import top.cywin.onetv.movie.event.NavigateToSearchResultsEvent
// 🔥 添加缺失的事件类导入
import top.cywin.onetv.movie.event.NavigateToLiveEvent
import top.cywin.onetv.movie.event.ShowHistoryScreenEvent
import top.cywin.onetv.movie.event.ShowFavoritesScreenEvent
import top.cywin.onetv.movie.event.ShowSiteSelectorEvent
import top.cywin.onetv.movie.event.ShowRouteSelectorEvent
import top.cywin.onetv.movie.event.RouteChangeEvent
import top.cywin.onetv.movie.event.ShowSearchScreenEvent
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Site
import top.cywin.onetv.movie.ui.model.VodConfigUrl
import top.cywin.onetv.movie.ui.model.HomeCategorySection
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.model.CategoryInfo
import top.cywin.onetv.movie.bean.Class
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.ui.components.SiteSelector
import top.cywin.onetv.movie.navigation.MovieRoutes
import top.cywin.onetv.movie.ui.components.MovieCard
import top.cywin.onetv.movie.ui.components.QuickCategoryGrid
import top.cywin.onetv.movie.ui.components.RouteSelector

/**
 * OneTV Movie首页 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieHomeScreen(
    navController: NavController,
    viewModel: MovieViewModel = viewModel {
        Log.d("ONETV_MOVIE_HOME", "🎬 [第4阶段] 开始创建MovieViewModel实例")
        MovieViewModel()
    }
) {
    Log.d("ONETV_MOVIE_HOME", "🎬 [第4阶段] MovieHomeScreen组件开始初始化")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:58")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

    // ✅ 强制触发ViewModel初始化 - 通过访问ViewModel实例
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 强制触发ViewModel初始化")
    val viewModelInstance = viewModel // 强制创建ViewModel实例
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] ViewModel实例已创建: ${viewModelInstance.javaClass.simpleName}")

    // ✅ 收集UI状态 - 使用多种方式确保状态同步
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 开始收集UI状态")
    val uiState by viewModelInstance.uiState.collectAsStateWithLifecycle()
    val navigationEvent by viewModelInstance.navigationEvent.collectAsStateWithLifecycle()

    // 🎯 调试：监控navigationEvent状态变化
    Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 当前navigationEvent状态: ${navigationEvent?.javaClass?.simpleName ?: "null"}")

    // ✅ 监听立即导航事件
    DisposableEffect(Unit) {
        val eventListener = object {
            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onNavigateToSearchResults(event: NavigateToSearchResultsEvent) {
                Log.d("ONETV_NAVIGATION", "🚀 [修复逻辑] 立即导航到搜索结果列表: ${event.keyword}")
                try {
                    navController.navigate(MovieRoutes.searchResultList(event.keyword))
                    Log.d("ONETV_NAVIGATION", "✅ [修复逻辑] 立即导航成功")
                } catch (e: Exception) {
                    Log.e("ONETV_NAVIGATION", "❌ [修复逻辑] 立即导航失败", e)
                }
            }

            // 🔥 新增：首页顶端功能按钮事件监听
            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onNavigateToLive(event: NavigateToLiveEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_EVENT] 收到返回直播事件")
                try {
                    // 🔥 修复：返回主应用TV模块的首页直播，直接退出Movie模块
                    navController.popBackStack()
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_SUCCESS] 返回主应用直播成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_ERROR] 返回主应用直播失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onShowHistoryScreen(event: ShowHistoryScreenEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_EVENT] 收到显示历史事件")
                try {
                    // 🔥 按照架构：导航到完整的历史记录屏幕
                    navController.navigate(MovieRoutes.HISTORY)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_SUCCESS] 导航到历史记录屏幕成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_ERROR] 导航到历史记录屏幕失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onShowFavoritesScreen(event: ShowFavoritesScreenEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_EVENT] 收到显示收藏事件")
                try {
                    // 🔥 按照架构：导航到完整的收藏屏幕
                    navController.navigate(MovieRoutes.FAVORITES)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_SUCCESS] 导航到收藏屏幕成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_ERROR] 导航到收藏屏幕失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onShowSiteSelector(event: ShowSiteSelectorEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_EVENT] 收到显示站点选择器事件，站点数量: ${event.sites.size}")
                try {
                    // 🔥 修复：显示站点选择器，包含站点列表
                    viewModelInstance.showSiteSelector(event.sites)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_SUCCESS] 站点选择器显示成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_ERROR] 显示站点选择器失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onShowSearchScreen(event: ShowSearchScreenEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_EVENT] 收到显示搜索事件")
                try {
                    // 🔥 修复：导航到标准搜索界面
                    navController.navigate(MovieRoutes.SEARCH)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_SUCCESS] 搜索界面显示成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_ERROR] 显示搜索界面失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onShowRouteSelector(event: ShowRouteSelectorEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SELECTOR_EVENT] 收到显示线路选择器事件，线路数量: ${event.routes.size}")
                try {
                    // 🔥 显示线路选择器，供用户选择线路并重新加载配置
                    viewModelInstance.showRouteSelector(event.routes)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SELECTOR_SUCCESS] 线路选择器显示成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SELECTOR_ERROR] 线路选择器显示失败: ${e.message}", e)
                }
            }

            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onRouteChange(event: RouteChangeEvent) {
                val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_CHANGE_EVENT] 收到线路切换事件: ${event.routeUrl}")
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_CHANGE_EVENT", "收到线路切换事件: ${event.routeUrl}")
                try {
                    // 🔥 触发线路配置重新加载 - 创建VodConfigUrl对象
                    val routeConfig = VodConfigUrl(name = "切换线路", url = event.routeUrl)
                    viewModelInstance.selectRoute(routeConfig)
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_CHANGE_SUCCESS] 线路切换处理成功")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_CHANGE_SUCCESS", "线路切换处理成功")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_CHANGE_ERROR] 线路切换处理失败: ${e.message}", e)
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_CHANGE_ERROR", "线路切换处理失败: ${e.message}")
                }
            }
        }

        // 注册EventBus监听
        if (!EventBus.getDefault().isRegistered(eventListener)) {
            EventBus.getDefault().register(eventListener)
        }

        onDispose {
            if (EventBus.getDefault().isRegistered(eventListener)) {
                EventBus.getDefault().unregister(eventListener)
            }
        }
    }

    // 🔥 原版FongMi_TV直接导航支持：设置NavController
    LaunchedEffect(Unit) {
        Log.d("ONETV_NAVIGATION", "🔥 [原版直接导航] 设置NavController到ViewModel")
        viewModelInstance.setNavController(navController)
        Log.d("ONETV_NAVIGATION", "✅ [原版直接导航] NavController设置完成")
    }

    // ✅ 监听导航事件 - 保留原有机制作为备用
    LaunchedEffect(navigationEvent) {
        Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] LaunchedEffect触发检查，navigationEvent: ${navigationEvent?.javaClass?.simpleName ?: "null"}")

        navigationEvent?.let { event ->
            Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] LaunchedEffect触发，收到导航事件: ${event.javaClass.simpleName}")
            when (event) {
                is NavigationEvent.NavigateToDetail -> {
                    Log.d("ONETV_NAVIGATION", "🚀 [FongMi_TV兼容] 导航到详情页: ${event.movieName}")
                    Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 导航参数: vodId=${event.vodId}, siteKey=${event.siteKey}")
                    try {
                        val route = MovieRoutes.detail(event.vodId, event.siteKey)
                        Log.d("ONETV_NAVIGATION", "🔗 [电影ID跟踪] 导航路由: $route")
                        navController.navigate(route)
                        Log.d("ONETV_NAVIGATION", "✅ [FongMi_TV兼容] 导航成功")
                        Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 成功导航到详情页面")

                        // 🔥 关键修复：清理导航事件，避免重复触发
                        viewModelInstance.clearNavigationEvent()
                    } catch (e: Exception) {
                        Log.e("ONETV_NAVIGATION", "❌ [FongMi_TV兼容] 导航失败", e)
                        Log.e("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 导航失败: ${e.message}")
                    }
                }
                is NavigationEvent.NavigateToCategory -> {
                    Log.d("ONETV_NAVIGATION", "🚀 导航到分类页: ${event.typeName}")
                    navController.navigate(MovieRoutes.category(event.typeId))
                    viewModelInstance.clearNavigationEvent()
                }
                is NavigationEvent.NavigateToSearchResultList -> {
                    Log.d("ONETV_NAVIGATION", "🚀 [修改逻辑] 导航到搜索结果列表页: ${event.keyword}")
                    Log.d("ONETV_NAVIGATION", "📋 [修改逻辑] 搜索结果数: ${event.results.size}")
                    try {
                        navController.navigate(MovieRoutes.searchResultList(event.keyword))
                        Log.d("ONETV_NAVIGATION", "✅ [修改逻辑] 导航到搜索结果列表成功")
                        viewModelInstance.clearNavigationEvent()
                    } catch (e: Exception) {
                        Log.e("ONETV_NAVIGATION", "❌ [修改逻辑] 导航失败", e)
                    }
                }
                else -> {
                    Log.d("ONETV_NAVIGATION", "🔄 其他导航事件: $event")
                }
            }
        }
    }

    // ✅ 添加强制重组触发器
    var recompositionTrigger by remember { mutableStateOf(0) }

    // ✅ 监听ViewModel状态变化，强制重组 - 修复状态同步问题
    LaunchedEffect(viewModelInstance.uiState) {
        viewModelInstance.uiState.collect { state ->
            Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] StateFlow状态变化: showWelcomeScreen=${state.showWelcomeScreen}")
            // ✅ 强制重组，确保UI及时响应状态变化
            recompositionTrigger = System.currentTimeMillis().toInt()
        }
    }

    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] UI状态收集完成: ${uiState.javaClass.simpleName}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 当前UI状态 - isLoading: ${uiState.isLoading}, error: ${uiState.error}")

    // ✅ 监听UI状态变化，添加调试日志和异常恢复机制
    LaunchedEffect(uiState.showWelcomeScreen, uiState.isLoading, uiState.error) {
        Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] UI状态变化监听触发")
        Log.d("ONETV_MOVIE_HOME", "📊 showWelcomeScreen: ${uiState.showWelcomeScreen}")
        Log.d("ONETV_MOVIE_HOME", "📊 isLoading: ${uiState.isLoading}")
        Log.d("ONETV_MOVIE_HOME", "📊 error: ${uiState.error}")
        Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

        // ✅ 如果状态变为欢迎界面，强制重组
        if (uiState.showWelcomeScreen) {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 检测到欢迎界面状态，准备重组")
        }

        // ✅ 修复：添加异常恢复机制，但排除站点选择器场景
        if (uiState.isLoading && !uiState.showWelcomeScreen && uiState.error == null) {
            kotlinx.coroutines.delay(10000) // 延长等待时间到10秒，避免误触发
            // 检查是否仍然在加载且没有内容
            val currentState = viewModelInstance.uiState.value
            if (currentState.isLoading && currentState.recommendMovies.isEmpty() &&
                currentState.categories.isEmpty() && currentState.homeCategories.isEmpty() &&
                !currentState.showSiteSelector) { // 🔥 修复：如果正在显示站点选择器，不触发刷新
                Log.w("ONETV_MOVIE_HOME", "⚠️ [第8阶段] 检测到长时间加载无响应，可能是状态更新失败")
                Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 尝试刷新状态（排除站点选择器场景）")
                viewModelInstance.refresh()
            } else if (currentState.showSiteSelector) {
                Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 正在显示站点选择器，跳过异常恢复")
            }
        }
    }

    // ✅ 检查是否有初始化错误
    if (uiState.error != null) {
        Log.e("ONETV_MOVIE_HOME", "❌ [第4阶段] MovieHomeScreen初始化错误: ${uiState.error}")
    } else {
        Log.d("ONETV_MOVIE_HOME", "✅ [第4阶段] MovieHomeScreen初始化成功")
    }



    // ✅ 修复：UI准备好后手动加载首页数据，但避免在站点选择器显示时重复触发
    LaunchedEffect(Unit) { // 🔥 修复：使用Unit作为key，确保只执行一次
        Log.d("ONETV_MOVIE_HOME", "🚀 [第5阶段] UI准备完成，开始加载首页数据")
        // 🔥 修复：检查是否已有数据，避免重复加载
        val currentState = viewModelInstance.uiState.value
        if (currentState.categories.isEmpty() && currentState.recommendMovies.isEmpty() &&
            !currentState.isLoading && !currentState.showSiteSelector) {
            Log.d("ONETV_MOVIE_HOME", "🔄 [第5阶段] 首次加载或数据为空，开始加载首页数据")
            viewModelInstance.loadHomeData()
        } else {
            Log.d("ONETV_MOVIE_HOME", "✅ [第5阶段] 数据已存在或正在加载，跳过重复加载")
        }
    }

    // ✅ UI内容渲染 - 强制状态同步，修复欢迎界面显示问题
    val currentState = viewModelInstance.uiState.value // 直接获取最新状态

    // ✅ 修复延迟重组机制，确保状态更新后能够正确重组
    LaunchedEffect(uiState.showWelcomeScreen, currentState.showWelcomeScreen) {
        val shouldShowWelcome = uiState.showWelcomeScreen || currentState.showWelcomeScreen
        if (shouldShowWelcome) {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 检测到欢迎界面状态变化，立即强制重组")
            Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] uiState.showWelcomeScreen: ${uiState.showWelcomeScreen}")
            Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] currentState.showWelcomeScreen: ${currentState.showWelcomeScreen}")
            // ✅ 立即重组，不延迟
            recompositionTrigger = System.currentTimeMillis().toInt()
        }
    }
    Log.d("ONETV_MOVIE_HOME", "🎬 [第8阶段] 开始UI内容渲染")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:154")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 重组触发器: $recompositionTrigger")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] UI状态检查(collectAsStateWithLifecycle) - showWelcomeScreen: ${uiState.showWelcomeScreen}, isLoading: ${uiState.isLoading}, error: ${uiState.error}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 当前状态检查(直接获取) - showWelcomeScreen: ${currentState.showWelcomeScreen}, isLoading: ${currentState.isLoading}, error: ${currentState.error}")

    // ✅ 修复状态选择逻辑 - 优先使用最新状态，特别是欢迎界面状态
    val renderState = when {
        currentState.showWelcomeScreen || uiState.showWelcomeScreen -> {
            // 如果任一状态显示欢迎界面，优先使用显示欢迎界面的状态
            if (currentState.showWelcomeScreen) currentState else uiState
        }
        else -> uiState // 其他情况使用收集的状态
    }
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 最终渲染状态 - showWelcomeScreen: ${renderState.showWelcomeScreen}, isLoading: ${renderState.isLoading}, error: ${renderState.error}")

    // ✅ 重构UI渲染逻辑 - 将配置加载状态集成到欢迎界面中
    when {
        renderState.showWelcomeScreen -> {
            // ✅ 只有明确需要显示欢迎页面时才显示（配置加载或错误状态）
            // 删除重复日志记录，避免Compose重组时的日志重复

            WelcomeScreen(
                onConfigSetup = {
                    Log.d("VOD_FLOW", "[WELCOME_CONFIG_CLICK] 用户点击配置按钮")
                    navController.navigate(MovieRoutes.CONFIG)
                },
                // ✅ 传递两阶段加载状态
                isLoadingConfig = renderState.isLoadingConfig,
                isLoadingRecommend = renderState.isLoadingRecommend,
                configLoadProgress = renderState.configLoadProgress,
                recommendLoadProgress = renderState.recommendLoadProgress,
                loadingMessage = renderState.loadingMessage.ifEmpty {
                    when {
                        renderState.isLoadingConfig -> "正在加载配置文件...速度与当前网络环境有关"
                        renderState.isLoadingRecommend -> "正在加载推荐内容..."
                        else -> "正在加载..."
                    }
                },
                configError = renderState.error,
                onRetryConfig = {
                    Log.d("VOD_FLOW", "[WELCOME_RETRY_CLICK] 用户点击重试按钮")
                    viewModelInstance.refresh()
                }
            )
        }

        renderState.isStoreHouseIndex && renderState.selectedRoute == null -> {
            Log.d("ONETV_MOVIE_HOME", "🏪 [第8阶段] 渲染仓库选择界面 (使用最终渲染状态)")
            RouteSelectionScreen(
                routes = renderState.availableRoutes,
                onRouteSelect = { route ->
                    viewModelInstance.selectRoute(route)
                }
            )
        }
        else -> {
            // 删除重复日志记录，避免Compose重组时的日志重复
            HomeContent(
                uiState = renderState,
                onRefresh = { viewModelInstance.refresh() },
                onMovieClick = { movie ->
                    Log.d("ONETV_MOVIE_CLICK", "🎬 [FongMi_TV兼容] 用户点击推荐影片: ${movie.vodName}")
                    Log.d("ONETV_MOVIE_CLICK", "📊 [FongMi_TV兼容] 影片信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")

                    // 恢复原版逻辑：转换MovieItem为Vod类型，走完整的10步转换链路
                    val vod = Vod().apply {
                        setVodId(movie.vodId)
                        setVodName(movie.vodName)
                        setVodPic(movie.vodPic)
                        setSite(Site().apply { setKey(movie.siteKey) })
                    }
                    viewModel.onMovieClick(vod)
                },
                onCategoryClick = { category ->
                    // 删除重复日志记录，避免Compose重组时的日志重复

                    // 🔥 修复逻辑：直接在主页显示分类内容，不跳转页面
                    viewModel.loadCategoryContent(category.typeId, category.typeName)
                },
                onSearchClick = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_CLICK] 用户点击搜索按钮")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_CLICK", "进入搜索界面")

                    try {
                        // 🔥 实现搜索功能 - 导航到搜索界面
                        navController.navigate(MovieRoutes.SEARCH)
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_SUCCESS] 搜索功能执行成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_ERROR] 搜索功能失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_ERROR", "搜索失败: ${e.message}")
                    }
                },
                onSettingsClick = {
                    Log.d("ONETV_SETTINGS_CLICK", "⚙️ 用户点击设置按钮")
                    Log.d("ONETV_SETTINGS_CLICK", "🚀 开始导航到设置页面")
                    try {
                        navController.navigate(MovieRoutes.SETTINGS)
                        Log.d("ONETV_SETTINGS_CLICK", "✅ 导航成功")
                    } catch (e: Exception) {
                        Log.e("ONETV_SETTINGS_CLICK", "❌ 导航失败", e)
                    }
                },
                onRouteSelect = { route ->
                    viewModelInstance.selectRoute(route)
                },
                onSiteSelect = { site ->
                    // 🔥 修复：只有选择不同站点时才触发切换，避免重复加载
                    val currentSite = uiState.currentSite
                    if (currentSite == null || currentSite.key != site.key) {
                        val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_SWITCH] 用户选择不同站点: ${site.name} (当前: ${currentSite?.name ?: "无"})")
                        viewModelInstance.switchSite(site.key)
                    } else {
                        Log.d("VOD_FLOW", "[SITE_SWITCH_SKIP] 用户选择相同站点，跳过切换: ${site.name}")
                    }
                },
                onHideSiteSelector = {
                    viewModelInstance.hideSiteSelector()
                },
                onShowRouteSelector = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_CLICK] 用户点击线路选择")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_CLICK", "显示线路选择")

                    try {
                        // 🔥 修复：显示线路选择器，供用户选择线路并重新加载配置
                        viewModelInstance.showRouteSelector()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SUCCESS] 线路选择功能执行成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_ERROR] 线路选择失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ROUTE_ERROR", "线路选择失败: ${e.message}")
                    }
                },
                onHideRouteSelector = {
                    viewModelInstance.hideRouteSelector()
                },
                onBackToLive = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_CLICK] 用户点击返回直播")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "LIVE_CLICK", "返回直播界面")

                    try {
                        // 🔥 修复：直接返回主应用TV模块的首页直播，不需要适配器
                        navController.popBackStack()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_SUCCESS] 返回主应用直播成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [LIVE_ERROR] 返回主应用直播失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "LIVE_ERROR", "返回直播失败: ${e.message}")
                    }
                },
                onShowHistory = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_CLICK] 用户点击观看历史")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HISTORY_CLICK", "显示观看历史")

                    try {
                        // 🔥 实现观看历史功能 - 通过适配器调用FongMi_TV功能
                        viewModelInstance.showHistoryScreen()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_SUCCESS] 观看历史功能执行成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_ERROR] 加载观看历史失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HISTORY_ERROR", "观看历史失败: ${e.message}")
                    }
                },
                onShowFavorites = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_CLICK] 用户点击收藏")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "FAVORITES_CLICK", "显示收藏界面")

                    try {
                        // 🔥 实现收藏功能 - 通过适配器调用FongMi_TV功能
                        viewModelInstance.showFavoritesScreen()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_SUCCESS] 收藏功能执行成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [FAVORITES_ERROR] 加载收藏失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "FAVORITES_ERROR", "收藏失败: ${e.message}")
                    }
                },
                onShowSiteSelector = {
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_CLICK] 用户点击站点选择")
                    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SITE_CLICK", "显示站点选择")

                    try {
                        // 🔥 修复：显示站点选择器，供用户选择站点并重新加载配置
                        viewModelInstance.showSiteSelector()
                        Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_SUCCESS] 站点选择功能执行成功")
                    } catch (e: Exception) {
                        Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SITE_ERROR] 站点选择失败: ${e.message}", e)
                        top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SITE_ERROR", "站点选择失败: ${e.message}")
                    }
                },
                onClearCategoryContent = {
                    // 🔥 修复逻辑：清除分类内容，返回主页
                    viewModelInstance.clearCategoryContent()
                },
                viewModel = viewModel  // 🔥 传递ViewModel引用
            )
        }
    }
}

@Composable
private fun HomeContent(
    uiState: MovieUiState,
    onRefresh: () -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onCategoryClick: (CategoryInfo) -> Unit,
    onSearchClick: () -> Unit,
    onSettingsClick: () -> Unit,
    onRouteSelect: (VodConfigUrl) -> Unit,
    onShowRouteSelector: () -> Unit,
    onHideRouteSelector: () -> Unit,
    onBackToLive: () -> Unit,
    onShowHistory: () -> Unit,
    onShowFavorites: () -> Unit,
    onShowSiteSelector: () -> Unit,
    onSiteSelect: (top.cywin.onetv.movie.bean.Site) -> Unit,  // 🔥 新增：站点选择回调
    onHideSiteSelector: () -> Unit,  // 🔥 新增：隐藏站点选择器回调
    onClearCategoryContent: () -> Unit,  // 🔥 新增：清除分类内容的回调
    viewModel: MovieViewModel  // 🔥 新增：ViewModel引用，用于分类电影点击
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        TopAppBar(
            title = {
                Text(
                    text = if (uiState.isStoreHouseIndex) uiState.storeHouseName else "OneTV 影视",
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            actions = {
                // 返回直播按钮
                TextButton(onClick = onBackToLive) {
                    Icon(
                        imageVector = Icons.Default.Tv,
                        contentDescription = "返回直播",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "直播",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 观看历史按钮
                TextButton(onClick = onShowHistory) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = "观看历史",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "历史",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 收藏按钮
                TextButton(onClick = onShowFavorites) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "收藏",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "收藏",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 🔥 修复：站点选择按钮
                TextButton(onClick = onShowSiteSelector) {
                    Icon(
                        imageVector = Icons.Default.Language,
                        contentDescription = "站点选择",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "站点",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 线路选择按钮
                TextButton(onClick = onShowRouteSelector) {
                    Icon(
                        imageVector = Icons.Default.Router,
                        contentDescription = "线路选择",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "线路",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 搜索按钮
                TextButton(onClick = onSearchClick) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "搜索",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 设置按钮
                TextButton(onClick = onSettingsClick) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "设置",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        )

        // 内容区域 - 显示正常的首页内容
        // ✅ 删除独立的EmptyContentScreen，所有错误状态都在欢迎页面中处理
        // 删除重复日志记录，避免Compose重组时的日志重复
        HomeContentScreen(
            uiState = uiState,
            onRefresh = onRefresh,
            onMovieClick = onMovieClick,
            onCategoryClick = onCategoryClick,
            onClearCategoryContent = onClearCategoryContent,
            viewModel = viewModel  // 🔥 传递ViewModel引用
        )

        // 线路选择对话框
        if (uiState.showRouteSelector) {
            RouteSelector(
                routes = uiState.availableRoutes,
                selectedRoute = uiState.selectedRoute,
                onRouteSelected = { route ->
                    // 🔥 修复：调用原版selectRoute方法触发线路切换
                    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ROUTE_SELECT] 用户选择线路: ${route.name}")
                    viewModel.selectRoute(route)
                },
                onDismiss = {
                    viewModel.hideRouteSelector()
                }
            )
        }

        // 站点选择对话框
        if (uiState.showSiteSelector) {
            SiteSelector(
                sites = uiState.availableSites,
                selectedSite = uiState.currentSite,
                onSiteSelected = { site ->
                    onSiteSelect(site)
                    onHideSiteSelector()
                },
                onDismiss = {
                    onHideSiteSelector()
                }
            )
        }
    }
}

// ✅ 按照指南添加必要的辅助Composable函数

// ✅ 已删除独立的LoadingScreen组件
// 配置加载状态现在集成在WelcomeScreen中处理

// ✅ EmptyContentScreen组件已删除 - 所有错误状态都在欢迎页面中处理

// ✅ 已删除独立的ErrorScreen组件
// 配置错误状态现在集成在WelcomeScreen中处理

@Composable
private fun RouteSelectionScreen(
    routes: List<VodConfigUrl>,
    onRouteSelect: (VodConfigUrl) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                Text(
                    text = "选择线路",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }

            items(routes) { route ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onRouteSelect(route) }
                ) {
                    Text(
                        text = route.name,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

@Composable
private fun HomeContentScreen(
    uiState: MovieUiState,
    onRefresh: () -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onCategoryClick: (CategoryInfo) -> Unit,
    onClearCategoryContent: () -> Unit,  // 🔥 新增：清除分类内容的回调
    viewModel: MovieViewModel  // 🔥 新增：ViewModel引用，用于分类电影点击
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 固定的分类导航栏（包含影视主页按钮）
        if (uiState.categories.isNotEmpty()) {
            CategoryGridWithHome(
                categories = uiState.categories,
                selectedCategoryName = uiState.currentCategoryName,  // 🔥 新增：传递当前选中的分类名称
                onCategoryClick = onCategoryClick,
                onHomeClick = {
                    // 删除重复日志记录，避免Compose重组时的日志重复
                    // 🔥 修复逻辑：清除分类内容，返回主页
                    onClearCategoryContent()
                }
            )
        }

        // 可滚动的内容区域
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {

        // 🔥 修复逻辑：根据是否有分类内容决定显示内容
        if (uiState.currentCategoryName != null && uiState.categoryMovies.isNotEmpty()) {
            // 显示分类内容
            // ✅ 注释掉分类标题文本，节省显示空间
            /*
            item {
                Text(
                    text = uiState.currentCategoryName,
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.padding(bottom = 12.dp)
                )
            }
            */

            // 🔥 修复逻辑：分类电影网格，一行显示5张电影卡片
            items(uiState.categoryMovies.chunked(5)) { movieRow ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    movieRow.forEach { movie ->
                        // 删除重复日志记录，避免Compose重组时的日志重复

                        MovieCard(
                            movie = movie,
                            onClick = {
                                // 删除重复日志记录，避免Compose重组时的日志重复

                                // 🔥 修复：直接使用与推荐电影相同的处理逻辑
                                val vod = Vod().apply {
                                    setVodId(movie.vodId)
                                    setVodName(movie.vodName)
                                    setVodPic(movie.vodPic)
                                    setSite(Site().apply { setKey(movie.siteKey) })
                                }
                                viewModel.onMovieClick(vod)
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    // 如果不足5个电影，添加空白占位
                    repeat(5 - movieRow.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        } else {
            // 显示推荐内容和各分类内容（原有逻辑）

            // 推荐内容轮播 - 只有在有内容时才显示
            if (uiState.recommendMovies.isNotEmpty()) {
                item {
                    RecommendCarousel(
                        movies = uiState.recommendMovies,
                        onMovieClick = onMovieClick
                    )
                }
            }
            // ✅ 删除了推荐内容加载提示，因为所有加载都在欢迎页面完成
            // 进入主界面时必须有完整的推荐内容

            // 🔥 修复逻辑：删除TV内容区域（各分类内容），只显示推荐内容
        }
        } // LazyColumn 结束
    } // Column 结束
}

@Composable
private fun RecommendCarousel(
    movies: List<MovieItem>,
    onMovieClick: (MovieItem) -> Unit
) {
    Column {
        // 🎬 注释掉"推荐内容"文本 - 按用户要求释放空间
        /*
        Text(
            text = "推荐内容",
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        */

        // 限制显示前10个推荐内容，使用网格布局
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier.height(480.dp), // 限制高度，显示2行
            contentPadding = PaddingValues(0.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(movies.take(10)) { movie ->
                MovieCard(
                    movie = movie,
                    onClick = { onMovieClick(movie) }
                )
            }
        }
    } // LazyColumn 结束 // Column 结束
}

@Composable
private fun CategoryGridWithHome(
    categories: List<CategoryInfo>,
    selectedCategoryName: String?,  // 🔥 新增：当前选中的分类名称
    onCategoryClick: (CategoryInfo) -> Unit,
    onHomeClick: () -> Unit
) {
    // 包含影视主页按钮的分类导航栏
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        LazyRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 影视主页按钮（固定在最左侧）
            item {
                val isHomeSelected = selectedCategoryName == null  // 🔥 判断是否选中主页
                Card(
                    modifier = Modifier.clickable { onHomeClick() },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isHomeSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.surface
                        }
                    ),
                    border = if (isHomeSelected) null else androidx.compose.foundation.BorderStroke(
                        1.dp,
                        MaterialTheme.colorScheme.outline
                    )
                ) {
                    Text(
                        text = "影视主页",
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isHomeSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                        fontWeight = if (isHomeSelected) FontWeight.Bold else FontWeight.Medium
                    )
                }
            }

            // 分类标签
            items(categories) { category ->
                val isSelected = selectedCategoryName == category.typeName  // 🔥 判断是否选中当前分类
                Card(
                    modifier = Modifier.clickable { onCategoryClick(category) },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.surface
                        }
                    ),
                    border = if (isSelected) null else androidx.compose.foundation.BorderStroke(
                        1.dp,
                        MaterialTheme.colorScheme.outline
                    )
                ) {
                    Text(
                        text = category.typeName ?: "未知分类",
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun CategoryGrid(
    categories: List<CategoryInfo>,
    onCategoryClick: (CategoryInfo) -> Unit
) {
    // 纯分类导航栏（无"分类"文本标题）
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        LazyRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 分类标签
            items(categories) { category ->
                Card(
                    modifier = Modifier.clickable { onCategoryClick(category) }
                ) {
                    Text(
                        text = category.typeName ?: "未知分类",
                        modifier = Modifier.padding(12.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun CategorySection(
    section: HomeCategorySection,
    onMovieClick: (MovieItem) -> Unit,
    onMoreClick: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = section.categoryName,
                style = MaterialTheme.typography.titleLarge
            )

            TextButton(onClick = onMoreClick) {
                Text("更多")
            }
        }

        // 分类内容使用网格布局，显示前10个
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier.height(480.dp), // 限制高度，显示2行
            contentPadding = PaddingValues(0.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(section.movies.take(10)) { movie ->
                MovieCard(
                    movie = movie,
                    onClick = { onMovieClick(movie) }
                )
            }
        }
    }
}

/**
 * 欢迎界面组件 - 漂亮的首次使用引导界面，集成配置加载状态
 */
@Composable
private fun WelcomeScreen(
    onConfigSetup: () -> Unit,
    modifier: Modifier = Modifier,
    // 新增参数：配置加载状态
    isLoadingConfig: Boolean = false,
    isLoadingRecommend: Boolean = false,
    configLoadProgress: Float = 0f,
    recommendLoadProgress: Float = 0f,
    loadingMessage: String = "",
    configError: String? = null,
    onRetryConfig: () -> Unit = {}
) {
    Log.d("ONETV_MOVIE_HOME", "🎨 [第8阶段] WelcomeScreen组件开始渲染")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:507")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

    // ✅ 修复重组触发器 - 确保组件能正确渲染
    val recompositionTrigger by remember { mutableStateOf(System.currentTimeMillis()) }
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 重组触发器: $recompositionTrigger")
    Log.d("ONETV_MOVIE_HOME", "✅ [第8阶段] WelcomeScreen组件正在渲染中...")

    // ✅ 重新设计布局 - 合理分配空间，确保所有内容在一屏内美观显示
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.05f)
                    )
                )
            )
            .padding(horizontal = 32.dp, vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 顶部区域：标题和图标（占用较少空间）
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 应用图标（更小尺寸）
            Icon(
                imageVector = Icons.Default.Movie,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            // 标题
            Text(
                text = "欢迎使用 OneTV 影视",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Bold
            )

            // 副标题
            Text(
                text = "您的专属影视娱乐平台",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        // 中间区域：功能介绍（紧凑布局）
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 三个功能特性横向排列
            FeatureItemCompact(
                icon = Icons.Default.CloudDownload,
                title = "海量资源",
                        description = "支持多种影视接口，内容丰富多样"
            )

            FeatureItemCompact(
                icon = Icons.Default.HighQuality,
                title = "高清播放",
                        description = "支持多种清晰度，观影体验极佳"
            )

            FeatureItemCompact(
                icon = Icons.Default.Speed,
                title = "快速解析",
                        description = "智能解析技术，播放流畅无卡顿"
            )
        }

        // 底部区域：配置提示和按钮，集成加载状态
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // ✅ 根据加载状态显示不同内容
            when {
                isLoadingConfig || isLoadingRecommend -> {
                    // 正在加载状态（配置文件或推荐内容）
                    // 删除重复日志记录，避免Compose重组时的日志重复

                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // 🔥 修复：线路切换时的进度显示逻辑
                        val totalProgress = when {
                            isLoadingConfig -> {
                                // 线路切换时，configLoadProgress直接反映实际进度
                                if (loadingMessage.contains("切换线路") || loadingMessage.contains("解析配置文件")) {
                                    configLoadProgress  // 直接使用配置加载进度
                                } else {
                                    configLoadProgress * 0.5f  // 普通配置加载占50%
                                }
                            }
                            isLoadingRecommend -> 0.5f + recommendLoadProgress * 0.5f  // 推荐内容加载占50%
                            else -> 1f
                        }

                        // 进度圆环和百分比
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                progress = totalProgress,
                                modifier = Modifier.size(48.dp),
                                strokeWidth = 4.dp,
                                color = MaterialTheme.colorScheme.primary
                            )

                            Text(
                                text = "${(totalProgress * 100).toInt()}%",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Bold
                            )
                        }

                        Text(
                            text = loadingMessage,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight.Medium
                        )

                        // 阶段提示
                        Text(
                            text = when {
                                isLoadingConfig -> "第1步：正在解析配置文件，获取影视站点信息"
                                isLoadingRecommend -> "第2步：正在获取推荐内容，为您准备精彩影视"
                                else -> "正在为您准备影视内容"
                            },
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                configError != null -> {
                    // 配置加载失败状态
                    // 删除重复日志记录，避免Compose重组时的日志重复

                    // 错误图标和信息
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = MaterialTheme.colorScheme.error
                        )

                        Text(
                            text = "暂无内容，请检查配置或刷新重试",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error,
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight.Medium
                        )

                        Text(
                            text = configError,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )

                        // 重试和配置按钮
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            OutlinedButton(
                                onClick = {
                                    Log.d("VOD_FLOW", "[WELCOME_RETRY] 用户点击重试按钮")
                                    onRetryConfig()
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("重试")
                            }

                            Button(
                                onClick = {
                                    Log.d("VOD_FLOW", "[WELCOME_CONFIG] 用户点击配置按钮")
                                    onConfigSetup()
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Settings,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("配置")
                            }
                        }
                    }
                }

                else -> {
                    // 🔥 修复：检查是否有配置，避免在线路切换时显示初始配置页面
                    if (configError == null && (isLoadingConfig || isLoadingRecommend)) {
                        // 如果正在加载，显示加载状态（这个分支实际上不会执行，因为上面已经处理了）
                        Text(
                            text = "正在加载...",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                            textAlign = TextAlign.Center
                        )
                    } else {
                        // 默认状态：初始欢迎界面（只在真正没有配置时显示）
                        Log.d("VOD_FLOW", "[WELCOME_INITIAL] 欢迎页面显示初始状态")

                        Text(
                            text = "请先配置影视接口以开始使用",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                            textAlign = TextAlign.Center
                        )
                    }

                    // 配置按钮
                    Button(
                        onClick = {
                            Log.d("VOD_FLOW", "[WELCOME_START] 用户点击开始配置按钮")
                            onConfigSetup()
                        },
                        modifier = Modifier
                            .fillMaxWidth(0.6f)
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        ),
                        elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "开始您的影视之旅",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 紧凑版功能介绍组件 - 用于欢迎界面的横向布局
 */
@Composable
private fun FeatureItemCompact(
    icon: ImageVector,
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.width(100.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(32.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )

        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 功能介绍项目组件（保留原版本用于其他地方）
 */
@Composable
private fun FeatureItem(
    icon: ImageVector,
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )

            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}