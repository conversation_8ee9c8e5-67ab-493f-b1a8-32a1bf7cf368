@file:OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)

package top.cywin.onetv.movie.ui.screens

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MoviePlayerViewModel
import top.cywin.onetv.movie.viewmodel.PlayerUiState
import top.cywin.onetv.movie.bean.Flag
import top.cywin.onetv.movie.ui.model.Episode
import top.cywin.onetv.movie.ui.components.PlayerControls
import top.cywin.onetv.movie.player.VideoController
import top.cywin.onetv.movie.player.rememberVideoController
import top.cywin.onetv.movie.player.Players
import kotlinx.coroutines.delay

/**
 * 播放器页面 - 直接移植原版播放器功能
 * 基于：FongMi_TV/src/leanback/java/top/cywin/onetv/vod/ui/activity/VideoActivity.java
 */
@Composable
fun MoviePlayerScreen(
    navController: NavController,
    siteKey: String,
    vodId: String,
    flag: Flag,
    episode: Episode,
    viewModel: MoviePlayerViewModel = viewModel()
) {
    val context = LocalContext.current
    val activity = context as Activity

    // 使用新的VideoController
    val controller = rememberVideoController(activity)

    // 收集ViewModel状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 控制栏显示状态
    var isControlsVisible by remember { mutableStateOf(false) }

    // 🔥 关键修复：处理返回键 - 播放器全屏时返回到详情页面
    BackHandler {
        android.util.Log.d("MoviePlayerScreen", "🔙 [返回逻辑] 播放器页面返回键被按下，返回到详情页面")
        navController.popBackStack()
    }

    // 初始化播放器 - 确保FlowID连续性
    LaunchedEffect(siteKey, vodId, episode) {
        // 🔥 获取全局FlowID，确保播放器使用相同的FlowID
        val globalFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getGlobalFlowId()
        android.util.Log.d("MoviePlayerScreen", "🔥 [FlowID修复] 播放器页面获取全局FlowID: $globalFlowId")

        viewModel.initPlayer(vodId, siteKey, episode.index, globalFlowId)
        viewModel.selectFlag(flag)
    }

    // 自动隐藏控制栏
    LaunchedEffect(isControlsVisible) {
        if (isControlsVisible) {
            delay(5000) // 5秒后自动隐藏
            isControlsVisible = false
        }
    }

    // 处理播放器状态变化
    LaunchedEffect(uiState.playUrl) {
        if (uiState.playUrl.isNotEmpty()) {
            // 播放器准备就绪，开始播放
            controller.initialize()?.let { players ->
                // 创建Result对象用于播放
                val result = top.cywin.onetv.movie.bean.Result().apply {
                    setPlayUrl(uiState.playUrl)
                    setParse(0) // 不需要解析
                }
                players.start(result, false, 30000) // 30秒超时

                // 🔥 修复：设置播放进度监听器，定期保存观看历史
                controller.setOnTimeChangedListener { position, duration ->
                    viewModel.updatePlayProgress(position, duration)
                }
            }
        }
    }

    // 🔥 修复：添加播放器生命周期管理，确保退出时保存历史记录
    DisposableEffect(Unit) {
        onDispose {
            // 播放器页面销毁时，确保保存最后的播放历史
            val currentState = uiState
            if (currentState.movie != null && currentState.currentPosition > 0 && currentState.duration > 0) {
                android.util.Log.d("MoviePlayerScreen", "🔥 [DISPOSE] 播放器页面销毁，保存最终播放历史")
                viewModel.updatePlayProgress(currentState.currentPosition, currentState.duration)
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                isControlsVisible = !isControlsVisible
            }
    ) {
        // 播放器视图
        AndroidView(
            factory = { context ->
                val playerView = androidx.media3.ui.PlayerView(context)
                controller.initialize()?.let { players ->
                    // 设置播放器到视图
                    playerView.player = players.getExoPlayer()
                }
                playerView
            },
            modifier = Modifier.fillMaxSize()
        ) { playerView ->
            // 更新播放器视图
        }

        // 播放器控制栏
        PlayerControls(
            controller = controller,
            episodes = uiState.episodes,
            currentEpisodeIndex = uiState.currentEpisodeIndex,
            isVisible = isControlsVisible,
            onEpisodeSelect = { index ->
                if (index < uiState.episodes.size) {
                    val newEpisode = uiState.episodes[index]
                    viewModel.playEpisode(newEpisode, index)
                }
            },
            onRefresh = {
                // 重新播放当前剧集
                uiState.currentEpisode?.let { episode ->
                    viewModel.playEpisode(episode, uiState.currentEpisodeIndex)
                }
            },
            onChangeSource = {
                // 切换到下一个播放源
                val currentFlagIndex = uiState.playFlags.indexOf(uiState.currentFlag)
                if (currentFlagIndex >= 0 && currentFlagIndex < uiState.playFlags.size - 1) {
                    val nextFlag = uiState.playFlags[currentFlagIndex + 1]
                    // 需要转换PlayFlag为Flag
                    val flag = top.cywin.onetv.movie.bean.Flag().apply {
                        setFlag(nextFlag.flag)
                        setUrls(nextFlag.urls)
                    }
                    viewModel.selectFlag(flag)
                }
            },
            modifier = Modifier.align(Alignment.BottomCenter)
        )

        // 播放器内状态显示 - 参考TV模块的VideoPlayerError组件
        if (uiState.isLoading) {
            // 加载状态显示在播放器中央，不覆盖整个屏幕
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .background(
                        color = Color.Black.copy(alpha = 0.8f),
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 20.dp, vertical = 10.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(color = Color.White, strokeWidth = 2.dp)
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "正在解析播放地址...",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        } else if (uiState.error != null) {
            // 错误信息显示在播放器中央，不覆盖整个屏幕
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .background(
                        color = Color.Black.copy(alpha = 0.8f),
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 20.dp, vertical = 10.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "播放失败",
                        color = Color.Red,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = uiState.error ?: "未知错误",
                        color = Color.White.copy(alpha = 0.8f),
                        style = MaterialTheme.typography.bodySmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = { viewModel.retryPlay() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red.copy(alpha = 0.8f)
                        )
                    ) {
                        Text("重试", color = Color.White)
                    }
                }
            }
        }
    }

    // 释放资源
    DisposableEffect(Unit) {
        onDispose {
            controller.release()
        }
    }
}