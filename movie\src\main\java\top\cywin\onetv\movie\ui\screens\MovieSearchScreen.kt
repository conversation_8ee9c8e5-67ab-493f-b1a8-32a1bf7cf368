package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items as staggeredItems
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieSearchViewModel
import top.cywin.onetv.movie.viewmodel.SearchUiState
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.MovieApp
import android.util.Log
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.NavigateToSearchResultsEvent

/**
 * 🔥 搜索界面 - 按照用户要求重构UI
 * 左侧：搜索历史（只显示电影名字）+ 清空按钮
 * 中间：主要搜索操作区（搜索框、按钮、字母表、数字表）
 * 右侧：热门搜索榜（热门电视榜、热门电影榜）+ 关联搜索
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MovieSearchScreen(
    initialKeyword: String? = null,
    navController: NavController,
    viewModel: MovieSearchViewModel = viewModel {
        MovieSearchViewModel()
    }
) {
    val currentFlowId = top.cywin.onetv.movie.utils.VodFlowTracker.getCurrentFlowId() ?: "UNKNOWN"
    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [NEW_SEARCH_SCREEN] 新版搜索界面初始化")
    top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "NEW_SEARCH_SCREEN", "新版搜索界面")

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // ✅ 处理初始关键词
    LaunchedEffect(initialKeyword) {
        if (!initialKeyword.isNullOrEmpty()) {
            viewModel.updateKeyword(initialKeyword)
            viewModel.search(initialKeyword)
        }
    }

    // 🔥 加载热门数据 - 通过RepositoryAdapter获取实际API数据
    LaunchedEffect(Unit) {
        viewModel.loadHotSearchData()
    }

    // 🔥 EventBus监听 - 处理搜索结果导航事件
    DisposableEffect(Unit) {
        val eventSubscriber = object {
            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onNavigateToSearchResults(event: NavigateToSearchResultsEvent) {
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [NAVIGATE_TO_SEARCH_RESULTS_EVENT] 收到搜索结果导航事件: keyword=${event.keyword}")
                try {
                    // 🔥 导航到搜索结果列表界面 - 使用正确的路由
                    navController.navigate(top.cywin.onetv.movie.navigation.MovieRoutes.searchResultList(event.keyword))
                    Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [NAVIGATE_TO_SEARCH_RESULTS_SUCCESS] 成功导航到搜索结果列表界面: ${event.keyword}")
                } catch (e: Exception) {
                    Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [NAVIGATE_TO_SEARCH_RESULTS_ERROR] 导航到搜索结果列表界面失败: ${e.message}")
                }
            }
        }

        EventBus.getDefault().register(eventSubscriber)

        onDispose {
            EventBus.getDefault().unregister(eventSubscriber)
        }
    }

    // 🔥 新版搜索界面布局
    NewSearchContent(
        uiState = uiState,
        onBack = { 
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_BACK] 用户返回")
            navController.popBackStack() 
        },
        onKeywordChange = { keyword -> viewModel.updateKeyword(keyword) },
        onSearch = { keyword -> 
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_EXECUTE] 执行搜索: $keyword")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_EXECUTE", "搜索关键词: $keyword")
            viewModel.search(keyword) 
        },
        onMovieClick = { movie ->
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_MOVIE_CLICK] 搜索结果点击: ${movie.vodName}")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_MOVIE_CLICK", "点击影片: ${movie.vodName}")

            // 🔥 修复：记录用户最终选择的电影名称到搜索历史
            viewModel.saveMovieClickHistory(movie.vodName)

            // 🔥 修复：按照用户要求，点击搜索结果应该立即进入搜索结果列表界面
            // 1）立即进入搜索结果列表界面
            // 2）以该电影名字进行多站搜索
            // 3）搜索结果动态展示
            try {
                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_RESULT_NAVIGATION] 导航到搜索结果列表: ${movie.vodName}")
                top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "SEARCH_RESULT_NAVIGATION", "导航到搜索结果列表: ${movie.vodName}")

                // 🔥 关键修复：使用电影名字作为搜索关键字，触发搜索模式
                val movieApp = top.cywin.onetv.movie.MovieApp.getInstance()
                movieApp.repositoryAdapter.handleMovieClick(
                    "msearch:${movie.vodName}", // 🔥 使用msearch前缀触发搜索模式
                    movie.vodName,
                    movie.siteKey
                )

                Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_RESULT_NAVIGATION_SUCCESS] 搜索结果导航成功")
            } catch (e: Exception) {
                Log.e("VOD_FLOW", "[FlowID:$currentFlowId] [SEARCH_RESULT_NAVIGATION_ERROR] 搜索结果导航失败: ${e.message}")
            }
        },
        onHistoryClick = { keyword -> 
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_CLICK] 历史记录点击: $keyword")
            viewModel.searchFromHistory(keyword) 
        },
        onHistoryDelete = { keyword ->
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HISTORY_DELETE] 删除历史记录: $keyword")
            viewModel.deleteSearchHistoryItem(keyword)
        },
        onClearHistory = { 
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [CLEAR_HISTORY] 清空搜索历史")
            viewModel.clearSearchHistory() 
        },
        onClearSearch = { viewModel.clearSearch() },
        onAlphabetClick = { letter ->
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [ALPHABET_CLICK] 字母点击: $letter")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "ALPHABET_CLICK", "字母点击: $letter")

            // 🔥 修复：追加字符而不是替换
            val currentKeyword = uiState.searchKeyword
            val newKeyword = currentKeyword + letter
            viewModel.updateKeyword(newKeyword)
        },
        onNumberClick = { number ->
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [NUMBER_CLICK] 数字点击: $number")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "NUMBER_CLICK", "数字点击: $number")

            // 🔥 修复：追加字符而不是替换
            val currentKeyword = uiState.searchKeyword
            val newKeyword = currentKeyword + number.toString()
            viewModel.updateKeyword(newKeyword)
        },
        onHotMovieClick = { movie ->
            Log.d("VOD_FLOW", "[FlowID:$currentFlowId] [HOT_MOVIE_CLICK] 热门影片点击: $movie")
            top.cywin.onetv.movie.utils.VodFlowTracker.logFlowStep(currentFlowId, "HOT_MOVIE_CLICK", "热门影片点击: $movie")

            // 🔥 修复：记录用户最终选择的电影名称到搜索历史
            viewModel.saveMovieNameToHistory(movie)
            viewModel.updateKeyword(movie)
            viewModel.search(movie)
        }
    )
}

@Composable
private fun NewSearchContent(
    uiState: SearchUiState,
    onBack: () -> Unit,
    onKeywordChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onHistoryClick: (String) -> Unit,
    onHistoryDelete: (String) -> Unit,
    onClearHistory: () -> Unit,
    onClearSearch: () -> Unit,
    onAlphabetClick: (String) -> Unit,
    onNumberClick: (Int) -> Unit,
    onHotMovieClick: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 🔥 注释掉顶部工具栏 - 按照用户要求，太占空间
        /*
        TopAppBar(
            title = { Text("搜索影视") },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                }
            }
        )
        */

        // 🔥 主要内容区域 - 三列布局，调整比例
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 🔥 左侧：搜索历史区域
            SearchHistorySection(
                modifier = Modifier.weight(0.25f),
                searchHistory = uiState.searchHistory,
                onHistoryClick = onHistoryClick,
                onHistoryDelete = onHistoryDelete,
                onClearHistory = onClearHistory
            )

            // 🔥 中间：主要搜索操作区 - 缩小1/4空间
            MainSearchSection(
                modifier = Modifier.weight(0.375f), // 从0.5减少到0.375
                keyword = uiState.searchKeyword,
                isLoading = uiState.isSearching,
                searchResults = uiState.searchResults,
                onKeywordChange = onKeywordChange,
                onSearch = onSearch,
                onMovieClick = onMovieClick,
                onClearSearch = onClearSearch,
                onAlphabetClick = onAlphabetClick,
                onNumberClick = onNumberClick
            )

            // 🔥 右侧：热门搜索区域 - 增加空间
            HotSearchSection(
                modifier = Modifier.weight(0.375f), // 从0.25增加到0.375
                keyword = uiState.searchKeyword,
                searchResults = uiState.searchResults,
                hotMovies = uiState.hotMovies,
                hotTvShows = uiState.hotTvShows,
                onHotMovieClick = onHotMovieClick,
                onMovieClick = onMovieClick
            )
        }
    }
}

// 🔥 左侧：搜索历史区域
@Composable
private fun SearchHistorySection(
    modifier: Modifier = Modifier,
    searchHistory: List<String>,
    onHistoryClick: (String) -> Unit,
    onHistoryDelete: (String) -> Unit,
    onClearHistory: () -> Unit
) {
    Card(
        modifier = modifier.fillMaxHeight(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 标题和清空按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "搜索历史",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 🔥 添加清空搜索历史功能按钮 - 按照用户要求实现
                if (searchHistory.isNotEmpty()) {
                    Button(
                        onClick = onClearHistory,
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error,
                            contentColor = MaterialTheme.colorScheme.onError
                        )
                    ) {
                        Text(
                            text = "清空历史",
                            style = MaterialTheme.typography.bodySmall,
                            fontSize = 10.sp
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 历史记录列表
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(searchHistory) { keyword ->
                    SearchHistoryItem(
                        keyword = keyword,
                        onClick = { onHistoryClick(keyword) },
                        onDelete = { onHistoryDelete(keyword) }
                    )
                }
            }
        }
    }
}

@Composable
private fun SearchHistoryItem(
    keyword: String,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 4.dp, horizontal = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = keyword,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        IconButton(
            onClick = onDelete,
            modifier = Modifier.size(24.dp)
        ) {
            Icon(
                Icons.Default.Close,
                contentDescription = "删除",
                modifier = Modifier.size(16.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

// 🔥 中间：主要搜索操作区
@Composable
private fun MainSearchSection(
    modifier: Modifier = Modifier,
    keyword: String,
    isLoading: Boolean,
    searchResults: List<MovieItem>,
    onKeywordChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onClearSearch: () -> Unit,
    onAlphabetClick: (String) -> Unit,
    onNumberClick: (Int) -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    Card(
        modifier = modifier.fillMaxHeight(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 🔥 搜索框占据一行
            OutlinedTextField(
                value = keyword,
                onValueChange = onKeywordChange,
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("输入影片名称-ONETV精彩有您") },
                singleLine = true,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        onSearch(keyword)
                        keyboardController?.hide()
                    }
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 🔥 搜索、清空、删除按钮在搜索栏下方
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        onSearch(keyword)
                        keyboardController?.hide()
                    },
                    enabled = keyword.isNotBlank() && !isLoading,
                    modifier = Modifier.weight(1f)
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("搜索")
                    }
                }

                Button(
                    onClick = onClearSearch,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清空")
                }

                // 🔥 删除按钮：在搜索框中往左逐个删除已经输入的字符 - 按照用户要求
                Button(
                    onClick = {
                        if (keyword.isNotEmpty()) {
                            onKeywordChange(keyword.dropLast(1))
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    ),
                    modifier = Modifier.weight(1f),
                    enabled = keyword.isNotEmpty()
                ) {
                    Text("删除")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // ✅ 按照用户要求：将26个字母与0-9数字合并在一起重新设计，采用相同设计风格，布满中间区域
            Log.d("VOD_FLOW", "[SEARCH_INPUT_GRID] 渲染合并的字母数字输入网格")

            CombinedInputGrid(
                modifier = Modifier.weight(1f), // 占据全部剩余空间
                onAlphabetClick = onAlphabetClick,
                onNumberClick = onNumberClick
            )

            // 🔥 底部搜索结果：现在取消在本区域内展示，搜索结果在最右侧的热门搜索版块展示
        }
    }
}

// ✅ 合并的字母数字输入网格 - 将26个字母与0-9数字合并，采用相同设计风格，布满中间区域
@Composable
private fun CombinedInputGrid(
    modifier: Modifier = Modifier,
    onAlphabetClick: (String) -> Unit,
    onNumberClick: (Int) -> Unit
) {
    // ✅ 合并字母和数字：26个字母 + 10个数字 = 36个元素
    val alphabet = listOf(
        "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
        "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
    )
    val numbers = listOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9)

    // ✅ 创建合并的输入项列表
    val combinedItems = mutableListOf<InputItem>()

    // 先添加字母
    alphabet.forEach { letter ->
        combinedItems.add(InputItem.Letter(letter))
    }

    // 再添加数字
    numbers.forEach { number ->
        combinedItems.add(InputItem.Number(number))
    }

    Log.d("VOD_FLOW", "[COMBINED_INPUT_GRID] 合并输入网格包含 ${combinedItems.size} 个元素")

    // ✅ 使用6列布局，36个元素正好6行，布满中间区域
    LazyVerticalGrid(
        columns = GridCells.Fixed(6), // 6列，6行，完美利用空间
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(combinedItems) { item ->
            when (item) {
                is InputItem.Letter -> {
                    InputGridButton(
                        text = item.letter,
                        onClick = { onAlphabetClick(item.letter) }
                    )
                }
                is InputItem.Number -> {
                    InputGridButton(
                        text = item.number.toString(),
                        onClick = { onNumberClick(item.number) }
                    )
                }
            }
        }
    }
}

// ✅ 输入项密封类
private sealed class InputItem {
    data class Letter(val letter: String) : InputItem()
    data class Number(val number: Int) : InputItem()
}

// ✅ 统一的输入网格按钮组件
@Composable
private fun InputGridButton(
    text: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .aspectRatio(1f)
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color.LightGray
        ),
        shape = RoundedCornerShape(6.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = Color.Black // ✅ 统一黑色字体
            )
        }
    }
}

// 🔥 保留原有的字母表网格组件（备用）
@Composable
private fun AlphabetGrid(
    modifier: Modifier = Modifier,
    onAlphabetClick: (String) -> Unit
) {
    val alphabet = listOf(
        "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
        "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
    )

    // 🔥 铺满中间区域，设计高效，26个字母和0-9数字在一页显示无需滚动
    LazyVerticalGrid(
        columns = GridCells.Fixed(6), // 6列，5行，更好地利用空间
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(alphabet) { letter ->
            // 🔥 使用正方形设计，黑色字体，紧凑设计
            Card(
                modifier = Modifier
                    .aspectRatio(1f)
                    .fillMaxWidth()
                    .clickable { onAlphabetClick(letter) },
                colors = CardDefaults.cardColors(
                    containerColor = Color.LightGray
                ),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(6.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = letter,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black // 🔥 黑色字体
                    )
                }
            }
        }
    }
}

// 🔥 数字表网格 - 重新设计：0-9数字，铺满剩余空间
@Composable
private fun NumberGrid(
    modifier: Modifier = Modifier,
    onNumberClick: (Int) -> Unit
) {
    val numbers = listOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9) // 0-9顺序排列

    // 🔥 设计高效，0-9数字在一页显示无需滚动
    LazyVerticalGrid(
        columns = GridCells.Fixed(5), // 5列，2行显示完毕
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(numbers) { number ->
            // 🔥 使用正方形设计，黑色字体，紧凑设计
            Card(
                modifier = Modifier
                    .aspectRatio(1f)
                    .fillMaxWidth()
                    .clickable { onNumberClick(number) },
                colors = CardDefaults.cardColors(
                    containerColor = Color.LightGray
                ),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(6.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = number.toString(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black // 🔥 黑色字体
                    )
                }
            }
        }
    }
}

// 🔥 搜索结果项 - 修复：只显示电影名字，其他信息无须展示
@Composable
private fun SearchResultItem(
    movie: MovieItem,
    onClick: () -> Unit
) {
    // 🔥 修复：简化显示，只显示电影名字，节省空间
    Text(
        text = movie.vodName,
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 6.dp, horizontal = 12.dp),
        style = MaterialTheme.typography.bodyMedium,
        color = MaterialTheme.colorScheme.onSurfaceVariant,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
    )
}

// 🔥 新增：搜索结果芯片组件 - 用于流式布局显示
@Composable
private fun SearchResultChip(
    movie: MovieItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Text(
            text = movie.vodName,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

// 🔥 右侧：热门搜索区域
@Composable
private fun HotSearchSection(
    modifier: Modifier = Modifier,
    keyword: String,
    searchResults: List<MovieItem>,
    hotMovies: List<String>,
    hotTvShows: List<String>,
    onHotMovieClick: (String) -> Unit,
    onMovieClick: (MovieItem) -> Unit
) {
    Card(
        modifier = modifier.fillMaxHeight(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "热门搜索",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 🔥 修复：只要搜索框有数据输入，右侧的热门电影、电影榜都要进行隐藏，将区域全部让出来供搜索结果展示
            if (keyword.isNotEmpty()) {
                Text(
                    text = "搜索结果 (${searchResults.size})",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 🔥 搜索结果在最右侧的热门搜索版块展示 - 修改为多列流式布局
                if (searchResults.isNotEmpty()) {
                    LazyVerticalStaggeredGrid(
                        columns = StaggeredGridCells.Adaptive(minSize = 120.dp),
                        modifier = Modifier.weight(1f), // 占据全部区域
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalItemSpacing = 4.dp,
                        contentPadding = PaddingValues(4.dp)
                    ) {
                        staggeredItems(searchResults) { movie ->
                            SearchResultChip(
                                movie = movie,
                                onClick = { onMovieClick(movie) }
                            )
                        }
                    }
                } else {
                    // 🔥 当搜索框有内容但没有搜索结果时的提示
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "正在搜索中...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {

                // 🔥 热门电影榜 - 根据接口文件中实际数据来展示，删除硬编码数据
                Text(
                    text = "热门电影榜",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 🔥 使用实际API数据，通过RepositoryAdapter获取热门电影
                LazyColumn(
                    modifier = Modifier.weight(0.4f),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(hotMovies) { movie ->
                        HotSearchItem(
                            title = movie,
                            onClick = { onHotMovieClick(movie) }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 🔥 热门电视榜 - 根据接口文件中实际数据来展示，删除硬编码数据
                Text(
                    text = "热门电视榜",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 🔥 使用实际API数据，通过RepositoryAdapter获取热门电视剧
                LazyColumn(
                    modifier = Modifier.weight(0.4f),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(hotTvShows) { tvShow ->
                        HotSearchItem(
                            title = tvShow,
                            onClick = { onHotMovieClick(tvShow) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun HotSearchItem(
    title: String,
    onClick: () -> Unit
) {
    Text(
        text = title,
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 4.dp, horizontal = 8.dp),
        style = MaterialTheme.typography.bodyMedium,
        color = MaterialTheme.colorScheme.onSurfaceVariant,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
    )
}

// 🔥 删除硬编码的电影数据库 - 按照用户要求，参考原版项目的用户搜索设计，调用原FongMi_TV项目的搜索电影功能
